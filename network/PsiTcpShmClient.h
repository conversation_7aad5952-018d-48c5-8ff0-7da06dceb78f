#pragma once
#include <cassert>
#include <string>
#include <iostream>
#include <thread>
#include <mutex>
#include <atomic>
#include <functional>

#include <boost/filesystem.hpp>
#include "PsiCfgLoader.h"
#include "PsiTcpShmConfCommon.h"
#include <chrono>

using namespace std;

// 包含模板基类定义
#include "PsiTcpShmClientCommon.h"

using namespace tcpshm;

class Client : public tcpshm::TcpShmClient<Client>
{
public:
    // 使用模板全名初始化基类
    Client(const std::string &ptcp_dir, const std::string &name, const ClientConf &client_conf)
        : tcpshm::TcpShmClient<Client>(ptcp_dir, name, client_conf)
        , conn(tcpshm::TcpShmClient<Client>::GetConnection())
    {
        // 存储基本配置信息
        m_index = client_conf.Index;
        m_cpuCore = client_conf.CpuCore;
        m_accountId = client_conf.AccountId;
        m_serverAddress = client_conf.ServerAddr;
        m_serverPort = client_conf.ServerPort;

        // 存储额外的配置信息用于打印
        m_clientName = name;
        m_ptcpDir = ptcp_dir;
        m_tcpRecvBufInitSize = client_conf.TcpRecvBufInitSize;
        m_tcpRecvBufMaxSize = client_conf.TcpRecvBufMaxSize;
        m_connectionTimeout = client_conf.ConnectionTimeout;
        m_heartBeatInterval = client_conf.HeartBeatInverval;
        m_tcpNoDelay = client_conf.TcpNoDelay;

        srand(static_cast<unsigned int>(time(nullptr)));
    }
    void Run();
    bool Poll();
    bool sendPing();
    bool sendStrategyGroupPageList();
    bool sendStrategyBuyInfoUpdate();
    bool sendTrade(PSIDoTraderStruct doTrader);
    void handlePing(const PingResp *ping_resp);
    void handleCmd(const cmdResp *cmd_resp);

    // 发送运行时参数到服务端
    bool sendClientRuntimeParams();

    // 设置运行时参数的方法
    void setRuntimeParams(int testDuration, int sendInterval, int messageSize, bool headlessMode, const std::string& configFile) {
        m_testDuration = testDuration;
        m_sendInterval = sendInterval;
        m_messageSize = messageSize;
        m_headlessMode = headlessMode;
        m_configFile = configFile;
    }

    using MessageHandler = std::function<void(const std::string&,const std::string&)>;
    using StatusHandler = std::function<void(bool,const std::string&)>;

    void setMessageHandler(MessageHandler handler) {
        m_messageHandler = handler;
    }
    void setStatusHandler(StatusHandler handler) {
        m_statusHandler = handler;
    }

private:
    // 修改 friend 声明为模板全名
    friend class tcpshm::TcpShmClient<Client>;

    // 回调函数声明
    void OnSystemError(const char *error_msg, int sys_errno);
    void OnLoginReject(const typename tcpshm::TcpShmClient<Client>::LoginRspMsg *login_rsp);
    int64_t OnLoginSuccess(const typename tcpshm::TcpShmClient<Client>::LoginRspMsg *login_rsp);
    void OnDisconnected(const char *reason, int sys_errno);
    void OnSeqNumberMismatch(uint32_t local_ack_seq,
                             uint32_t local_seq_start,
                             uint32_t local_seq_end,
                             uint32_t remote_ack_seq,
                             uint32_t remote_seq_start,
                             uint32_t remote_seq_end);
    void OnServerMsg(MsgHeader *header);

    MessageHandler m_messageHandler;
    StatusHandler m_statusHandler;
    bool baseMsgSent = false;

private:
    typename tcpshm::TcpShmClient<Client>::Connection &conn;
    std::thread worker_thread_;
    std::thread ping_thread_;
    std::mutex conn_mutex_; // 添加互斥锁
    std::atomic<bool> running_{true};
    std::mutex handle_mutex_;
    int64_t last_base_msg_time_ = 0; // 记录上次发送BaseMsg的时间

    std::mutex seq_mutex_;            // 序列号专用锁
    uint32_t current_seq_no_ = 1;     // 当前序列号（从1开始）
    uint32_t pending_seq_no_ = 0;     // 等待确认的序列号（0表示无等待）
    std::string pending_json_str_;    // 等待确认的消息内容
    bool waiting_for_ack_ = false;    // 等待确认标志


    std::mutex ping_mutex_;//心跳锁
    uint32_t current_ping_seq_no_ = 0;     // 当前序列号（从1开始）
    int64_t last_ping_time;//上一次的发送时间
   




public:
    int m_index = 0; // index
    int m_cpuCore = 0; // 绑定核心
    bool m_useShm = false; // 是否使用共享内存
    std::string m_serverAddress; // 服务器地址
    uint16_t m_serverPort = 0; // 服务器端口
    std::string m_accountId; // 账号

    // 添加更多配置信息用于打印
    std::string m_clientName; // 客户端名称
    std::string m_ptcpDir; // PTCP目录
    uint32_t m_tcpRecvBufInitSize = 0; // TCP接收缓冲区初始大小
    uint32_t m_tcpRecvBufMaxSize = 0; // TCP接收缓冲区最大大小
    int64_t m_connectionTimeout = 0; // 连接超时时间
    int64_t m_heartBeatInterval = 0; // 心跳间隔
    bool m_tcpNoDelay = false; // TCP无延迟

    // 运行时参数（可选，由外部设置）
    int m_testDuration = 0; // 测试持续时间（秒）
    int m_sendInterval = 0; // 发送间隔（微秒）
    int m_messageSize = 0; // 消息大小（字节）
    bool m_headlessMode = false; // 无界面模式
    std::string m_configFile; // 配置文件路径
    void Stop() {
        conn.Close();       // 关闭连接

        // 如果线程可连接，等待它结束
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        if (ping_thread_.joinable()) {
            ping_thread_.join();
        }
        std::cout << "PsiTcpShmClient close" << std::endl;
    }
    ~Client() {
        Stop();  // 析构时确保停止线程
        m_statusHandler = nullptr; // 防止悬空回调
    }
};
