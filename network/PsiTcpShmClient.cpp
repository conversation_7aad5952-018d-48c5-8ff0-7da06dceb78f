#include "PsiTcpShmClient.h"
#include <sstream>
#include <cstdio>

// 实现所有成员函数
void Client::Run() {
    // 强制输出一条stderr日志，确保在任何环境都可见
    std::fprintf(stderr, "[Client::Run] start name=%s headless=%d\n", m_clientName.c_str(), (int)m_headlessMode);
    // 打印所有命令行传递来的参数信息
    cout << "=== 客户端运行参数信息 ===" << endl;
    cout << "配置文件路径 (ConfigFile): " << m_configFile << endl;
    cout << "客户端名称 (ClientName): " << m_clientName << endl;
    cout << "PTCP目录 (PtcpDir): " << m_ptcpDir << endl;
    cout << "客户端索引 (Index): " << m_index << endl;
    cout << "CPU核心绑定 (CpuCore): " << m_cpuCore << endl;
    cout << "账号ID (AccountId): " << m_accountId << endl;
    cout << "服务器地址 (ServerAddress): " << m_serverAddress << endl;
    cout << "服务器端口 (ServerPort): " << m_serverPort << endl;
    cout << "使用共享内存 (UseShm): " << (m_useShm ? "true" : "false") << endl;
    cout << "TCP接收缓冲区初始大小 (TcpRecvBufInitSize): " << m_tcpRecvBufInitSize << " bytes" << endl;
    cout << "TCP接收缓冲区最大大小 (TcpRecvBufMaxSize): " << m_tcpRecvBufMaxSize << " bytes" << endl;
    cout << "连接超时时间 (ConnectionTimeout): " << m_connectionTimeout << " ms" << endl;
    cout << "心跳间隔 (HeartBeatInterval): " << m_heartBeatInterval << " ms" << endl;
    cout << "TCP无延迟 (TcpNoDelay): " << (m_tcpNoDelay ? "true" : "false") << endl;

    // 打印运行时参数（如果已设置）
    if (m_testDuration > 0 || m_sendInterval > 0 || m_messageSize > 0) {
        cout << "--- 运行时参数 ---" << endl;
        cout << "测试持续时间 (TestDuration): " << (m_testDuration > 0 ? std::to_string(m_testDuration) + " 秒" : "无限制") << endl;
        cout << "发送间隔 (SendInterval): " << m_sendInterval << " 微秒" << endl;
        cout << "消息大小 (MessageSize): " << m_messageSize << " 字节" << endl;
        cout << "无界面模式 (HeadlessMode): " << (m_headlessMode ? "true" : "false") << endl;
    }    
    cout << "=========================" << endl;

    if (!Connect(m_useShm, m_serverAddress.c_str(), m_serverPort, 0)) return;

    // 连接成功后先发送一次运行参数
    sendClientRuntimeParams();

    // 初始化发送时间为当前时间
    auto initial_time = std::chrono::system_clock::now();
    last_base_msg_time_ = std::chrono::duration_cast<std::chrono::milliseconds>(
                              initial_time.time_since_epoch()).count();
    // 创建并分离工作线程
    worker_thread_ = std::thread([this]() {
        while (running_ && !conn.IsClosed()) {
            auto now = std::chrono::system_clock::now();
            int64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                                       now.time_since_epoch()).count();
            // 每1秒发送一次StrategyBuyInfoUpdate
           /*  if (current_time - last_base_msg_time_ >= 1000) {
                if (sendStrategyBuyInfoUpdate()) {
                    last_base_msg_time_ = current_time; // 更新发送时间
                }
            } */

            if (m_useShm) {
                if (!Poll()) {
                    conn.Close();
                }
                PollShm();
            } else {
                if (!Poll()) {
                    conn.Close();
                }
            }

            PollTcp(current_time);
        }
        running_ = false; // 通知其他线程
    });
    // 启动定时Ping线程
    ping_thread_ = std::thread([this]() {
        while(running_) {
            auto now = std::chrono::system_clock::now();
            int64_t current_time = std::chrono::duration_cast<std::chrono::microseconds>(
                                       now.time_since_epoch()).count();
            cout << "ping_thread_" << endl;
            if (!sendPing()) {
                running_ = false;
                break;
            }
        }
    });
}

bool Client::Poll() {
    // 实现内容
    return true;
}

// 函数: sendClientRuntimeParams
// 作用: 把运行参数以 cmd=client_runtime_params 的 JSON 发到服务端
bool Client::sendClientRuntimeParams() {
    std::lock_guard<std::mutex> lock(conn_mutex_);
    if (conn.IsClosed()) return false;

    std::ostringstream json;
    json << "{";
    json << "\"client_name\":\"" << m_clientName << "\",";
    json << "\"config_file\":\"" << m_configFile << "\",";
    json << "\"server_addr\":\"" << m_serverAddress << "\",";
    json << "\"server_port\":" << m_serverPort << ",";
    json << "\"use_shm\":" << (m_useShm ? "true" : "false") << ",";
    json << "\"test_duration\":" << m_testDuration << ",";
    json << "\"send_interval\":" << m_sendInterval << ",";
    json << "\"message_size\":" << m_messageSize << ",";
    json << "\"headless\":" << (m_headlessMode ? "true" : "false");
    json << "}";

    std::string data = json.str();
    const char* json_data = data.c_str();
    uint32_t json_len = static_cast<uint32_t>(data.size());

    bool ok = conn.TrySendJsonMsg<cmdReq>(
        [json_data, json_len](cmdReq *req, char* region) {
            req->seq_no = 0;  // 非序列敏感
            req->token_len = 0; req->token[0] = '\0';
            req->cmd_len = strlen("client_runtime_params");
            strcpy(req->cmd, "client_runtime_params");
            req->data_len = json_len;
            memcpy(region, json_data, json_len);
        },
        true,
        json_len
    );

    return ok;
}



bool Client::sendPing() {
    if (!running_) return false;
    std::lock_guard<std::mutex> lock(conn_mutex_); // 加锁


    uint32_t current_seq;
    int64_t current_time;
    {
        std::lock_guard<std::mutex> ping_lock(ping_mutex_); // 为ping序列号加锁
        current_seq = ++current_ping_seq_no_;


            auto now_time = std::chrono::system_clock::now();
            current_time = std::chrono::duration_cast<std::chrono::microseconds>(
                now_time.time_since_epoch()).count();
    }




    cout << "ping-msg-seq: " << current_seq << ", send_time: " << current_time << endl;
    std::string simple_msg = "sent_ping,seq_no: " + std::to_string(current_seq) +
                           ", current_time: " + std::to_string(current_time) + "us";

    if (m_messageHandler) m_messageHandler("sent_ping",simple_msg);//将发送消息渲染到窗口

    bool send=conn.TrySendMsg<PingReq>([current_seq, current_time](PingReq *req) {
        req->seq_no = current_seq;
        req->send_time = current_time;
        strcpy(req->val, "ping");
    },true);
    if(send){
        last_ping_time=current_time;
    }
    // 使用可中断的睡眠
    for (int i = 0; i < 3 && running_; i++) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    return running_;
}

bool Client::sendStrategyGroupPageList() {
    std::lock_guard<std::mutex> lock(conn_mutex_); // 加锁

    if (conn.IsClosed()) return false;
    cout << "send sendStrategyGroupPageList" << endl;

    strategyGroupPageListReq dataObj;
    dataObj.order = "";
    dataObj.cmd = "strategy_group_page_list";
    dataObj.sort = 1;
    dataObj.pageNo = 1;
    dataObj.pageSize = 1;

    std::ostringstream jsonStream;
    jsonStream << "{"
               << "\"cmd\":\"" << dataObj.cmd << "\","
               << "\"requestId\":*************,"
               << "\"account\":\"580000\","
               << "\"userId\":\"e52b146749734c178ff762d3317a576f\","
               << "\"token\":\"8956b10b-3594-4ada-9733-bf4b73a6a64b\","
               << "\"data\":{"
               << "\"order\":\"" << dataObj.order << "\","
               << "\"sort\":" << dataObj.sort << ","
               << "\"pageNo\":" << dataObj.pageNo << ","
               << "\"pageSize\":" << dataObj.pageSize
               << "}}";

    std::string json_str = jsonStream.str();
    const char* json_data = json_str.c_str();
    uint32_t json_len = static_cast<uint32_t>(json_str.length());

    if (m_messageHandler) m_messageHandler("sent", json_str.c_str());

    // 关键修改：添加第三个参数 json_len
    // conn.TrySendJsonMsg<cmdReq>([json_data, json_len](cmdReq *req) {
    //     req->token_len = strlen("8956b10b-3594-4ada-9733-bf4b73a6a64b");
    //     strcpy(req->token, "8956b10b-3594-4ada-9733-bf4b73a6a64b");
    //     req->cmd_len = strlen("strategy_group_page_list");
    //     strcpy(req->cmd, "strategy_group_page_list");
    //     // 设置data部分
    //     req->data_len = json_len;
    //     memcpy(req->data, json_data, json_len);
    // }, true, json_len);  // 第三个参数：额外数据大小

    return true;
}

bool Client::sendStrategyBuyInfoUpdate() {
    std::lock_guard<std::mutex> lock(conn_mutex_); // 加锁
    if (conn.IsClosed()) return false;

    uint32_t seq_to_send;
    std::string json_to_send;
    bool is_retransmission = false;

    {
        std::lock_guard<std::mutex> seq_lock(seq_mutex_);

        if (waiting_for_ack_) {
            // 正在等待确认，重发上次的消息
            seq_to_send = pending_seq_no_;
            json_to_send = pending_json_str_;
            is_retransmission = true;
        } else {
            // 没有等待中的消息，创建新消息
            seq_to_send = current_seq_no_;

            // 构建新消息（原有JSON构造代码）
            std::ostringstream jsonStream;
            jsonStream << "{";
            jsonStream << "\"account\":\"580000\",";
            jsonStream << "\"cmd\":\"strategy_buy_info_update\",";
            jsonStream << "\"data\":{";
            jsonStream << "\"account\":\"580000\",";
            jsonStream << "\"name\":\"zidongzengjia_4\",";
            jsonStream << "\"params\":[";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"maxStockNum\",\"tempKeyName\":\"最多买入股票数\",\"tempKeyValue\":100,\"type\":1},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"isOverDel\",\"tempKeyName\":\"盘后立即删除\",\"tempKeyValue\":\"1\",\"type\":1},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"buyNum\",\"tempKeyName\":\"买入手数\",\"tempKeyValue\":\"\",\"type\":1},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"buyAmount\",\"tempKeyName\":\"买入金额\",\"tempKeyValue\":\"0\",\"type\":1},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"endTime\",\"tempKeyName\":\"结束时间\",\"tempKeyValue\":\"15:00:00\",\"type\":1},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"buyStopped\",\"tempKeyName\":\"买入停止\",\"tempKeyValue\":\"0\",\"type\":1},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"placeOrderTime\",\"tempKeyName\":\"下单时机\",\"tempKeyValue\":\"7\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"mainBuyAmount\",\"tempKeyName\":\"主买成交大于\",\"tempKeyValue\":\"1000\",\"type\":2},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"sealsAmount\",\"tempKeyName\":\"封单金额\",\"tempKeyValue\":\"1000\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"isOnlyHitBackseal\",\"tempKeyName\":\"是否只打回封\",\"tempKeyValue\":\"\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"isTboardOptimization\",\"tempKeyName\":\"是否T字板优化\",\"tempKeyValue\":\"0\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"isAutoOrderChargeback\",\"tempKeyName\":\"是否自动撤单\",\"tempKeyValue\":\"0\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"boardCancelOrderAmount\",\"tempKeyName\":\"板上撤单金额\",\"tempKeyValue\":\"1000\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"boardSmashOrderAmount\",\"tempKeyName\":\"板上砸单金额\",\"tempKeyValue\":\"1000\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"fewerSealsRatio\",\"tempKeyName\":\"封单减少比例\",\"tempKeyValue\":\"60\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"sealsTotalAmount\",\"tempKeyName\":\"封单总额\",\"tempKeyValue\":\"500\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"autoOrderChargebackDeadline\",\"tempKeyName\":\"自动撤单截止时间\",\"tempKeyValue\":\"15:00:00\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"isSmartOrder\",\"tempKeyName\":\"是否智能撤单\",\"tempKeyValue\":\"0\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"boardCancelOrderSeconds\",\"tempKeyName\":\"秒内板上撤单\",\"tempKeyValue\":\"1\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"boardSmashOrderSeconds\",\"tempKeyName\":\"秒内板上砸单\",\"tempKeyValue\":\"1\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"fewerSealsRatioSeconds\",\"tempKeyName\":\"秒内封单减少比例\",\"tempKeyValue\":\"1\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"cancelWaitTime\",\"tempKeyName\":\"撤单等待时间\",\"tempKeyValue\":\"300\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"amountBetweenOrder\",\"tempKeyName\":\"每次下单金额\",\"tempKeyValue\":\"10000\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"positionCancelOrder\",\"tempKeyName\":\"定位撤单\",\"tempKeyValue\":\"0\",\"type\":3},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"delayedOrderPlacement\",\"tempKeyName\":\"延迟下单\",\"tempKeyValue\":\"100\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"smashOrderPercentSeconds\",\"tempKeyName\":\"秒内砸单占比\",\"tempKeyValue\":\"1\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"smashOrderPercentMinuteNum\",\"tempKeyName\":\"分钟内砸单占比数\",\"tempKeyValue\":\"50\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"saleWaitTime\",\"tempKeyName\":\"等待时间\",\"tempKeyValue\":\"50\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"remainingSealAmount\",\"tempKeyName\":\"剩余封单金额小于\",\"tempKeyValue\":\"500\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"sealDecreaseRatio\",\"tempKeyName\":\"封单锐减比例\",\"tempKeyValue\":\"30\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"airborneStrength\",\"tempKeyName\":\"隔空强度\",\"tempKeyValue\":\",,\",\"tempKeyValueArr\":[\"\",\"\",\"\"],\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"sharpOrdersReductionRadio\",\"tempKeyName\":\"压单锐减\",\"tempKeyValue\":\"0.15\",\"type\":2},";
            jsonStream << "{\"isShow\":false,\"tempKey\":\"sharpOrdersAmount\",\"tempKeyName\":\"压单金额\",\"tempKeyValue\":\"1000\",\"type\":2},";
            jsonStream << "{\"isShow\":true,\"tempKey\":\"noSweepingDuration\",\"tempKeyName\":\"禁扫时长\",\"tempKeyValue\":\"5\",\"type\":2}";
            jsonStream << "],";
            jsonStream << "\"price\":0,";
            jsonStream << "\"stockCode\":\"\",";
            jsonStream << "\"stockPoolId\":\"f2689b9b6198415d84477e82b1b81b95\",";
            jsonStream << "\"strategyBuyId\":\"b6f3c76ca46f833bc15cf59335067bea\",";
            jsonStream << "\"strategyGroupId\":\"0fdf612505ca4dd3a2bcad27acd40f6e\",";
            jsonStream << "\"strategyTempId\":\"b3846ae111f9779caa7940a8e00c60eb\",";
            jsonStream << "\"subAccountGroup\":[";
            jsonStream << "{";
            jsonStream << "\"accountList\":[";
            jsonStream << "{\"accountDesc\":\"0514\",\"accountId\":\"*********\",\"id\":15184,\"isShow\":1,\"sort\":0},";
            jsonStream << "{\"accountDesc\":\"测试04\",\"accountId\":\"*********\",\"id\":15187,\"isShow\":1,\"sort\":1}";
            jsonStream << "],";
            jsonStream << "\"accountType\":\"3\",";
            jsonStream << "\"accountTypeDesc\":\"北交所\",";
            jsonStream << "\"modelType\":\"2\"";
            jsonStream << "},";
            jsonStream << "{";
            jsonStream << "\"accountList\":[";
            jsonStream << "{\"accountDesc\":\"子账户1233\",\"accountId\":\"*********\",\"id\":15185,\"isShow\":1,\"sort\":0},";
            jsonStream << "{\"accountDesc\":\"ceshi06\",\"accountId\":\"*********\",\"id\":15188,\"isShow\":1,\"sort\":1}";
            jsonStream << "],";
            jsonStream << "\"accountType\":\"2\",";
            jsonStream << "\"accountTypeDesc\":\"深交所\",";
            jsonStream << "\"modelType\":\"2\"";
            jsonStream << "},";
            jsonStream << "{";
            jsonStream << "\"accountList\":[";
            jsonStream << "{\"accountDesc\":\"333子312\",\"accountId\":\"*********\",\"id\":15186,\"isShow\":1,\"sort\":0}";
            jsonStream << "],";
            jsonStream << "\"accountType\":\"1\",";
            jsonStream << "\"accountTypeDesc\":\"上交所\",";
            jsonStream << "\"modelType\":\"2\"";
            jsonStream << "}";
            jsonStream << "],";
            jsonStream << "\"type\":1";
            jsonStream << "},";
            jsonStream << "\"requestId\":\"1753857345894300327\",";
            jsonStream << "\"token\":\"8956b10b-3594-4ada-9733-bf4b73a6a64b\"";
            jsonStream << "}";
            json_to_send = jsonStream.str();

            // 保存为待确认消息
            pending_seq_no_ = seq_to_send;
            pending_json_str_ = json_to_send;
            waiting_for_ack_ = true;
            current_seq_no_++;  // 递增序列号用于下一条消息
        }
    }

    // cout << "send sendStrategyBuyInfoUpdate, seq: " << seq_to_send
    //      << (is_retransmission ? " (retry)" : " (new)") << endl;

    const char* json_data = json_to_send.c_str();
    uint32_t json_len = static_cast<uint32_t>(json_to_send.length());

    // if (m_messageHandler) m_messageHandler("sent", json_to_send.c_str());

    // 发送消息（包含seq_no）
    bool send = conn.TrySendJsonMsg<cmdReq>(
        [json_data, json_len, seq_to_send](cmdReq *req, char* data_region) {
            req->seq_no = seq_to_send;  // 设置序列号
            req->token_len = strlen("8956b10b-3594-4ada-9733-bf4b73a6a64b");
            strcpy(req->token, "8956b10b-3594-4ada-9733-bf4b73a6a64b");
            req->cmd_len = strlen("strategy_buy_info_update");
            strcpy(req->cmd, "strategy_buy_info_update");
            req->data_len = json_len;
            memcpy(data_region, json_data, json_len);
        },
        true,  // is_client
        json_len
        );

    if(send)
    {
        // 提取响应中的序列号
        uint32_t resp_seq_no = seq_to_send;
        {
            std::lock_guard<std::mutex> seq_lock(seq_mutex_);

            // 如果是我们等待的序列号
            if (waiting_for_ack_ && resp_seq_no == pending_seq_no_) {
                waiting_for_ack_ = false;  // 清除等待标志
                pending_seq_no_ = 0;       // 清除待处理序列号
                pending_json_str_.clear(); // 清除缓存的消息
            }
        }
    }

    return true;
}

bool sendTrade(PSIDoTraderStruct doTrader) {
    // conn.TrySendMsg<TraderReq>([doTrader](TraderReq *req) {
    //     strcpy(req->clientId, this->m_accountId.c_str());
    //     //            strcpy(req->exchangeId,"1");
    //     memcpy(&req->_doTrader, &doTrader, sizeof(PSIDoTraderStruct));
    // },true);
    // cout << "send trade 1" << endl;
    return false;
}

void Client::handlePing(const PingResp *ping_resp) {
#ifdef _WIN32
    system("chcp 65001"); // Windows 控制台编码设置
#endif
    std::lock_guard<std::mutex> lock(handle_mutex_); // 加锁

    // 获取当前时间作为接收时间
    auto now_time = std::chrono::system_clock::now();
    int64_t receive_time = std::chrono::duration_cast<std::chrono::microseconds>(
        now_time.time_since_epoch()).count();

    // 计算接收时间与ack_time的时间差
    int64_t time_diff = receive_time - ping_resp->ack_time;

    // 简化的消息用于UI显示
    std::string simple_msg = "received_ping,seq_no: " + std::to_string(ping_resp->seq_no) +
                           ", ack_time: " + std::to_string(ping_resp->ack_time) + "us" +
                           ", receive_time: " + std::to_string(receive_time) + "us" +
                           ", time_diff: " + std::to_string(time_diff) + "us";

    // 在控制台打印详细信息
    std::cout << simple_msg << std::endl;

    // 向UI发送简化信息
    if (m_messageHandler) m_messageHandler("received_ping", simple_msg);
}

void Client::handleCmd(const cmdResp *cmd_resp) {
#ifdef _WIN32
    system("chcp 65001"); // Windows 控制台编码设置
#endif
    std::lock_guard<std::mutex> lock(handle_mutex_); // 加锁
    std::string msg = "Got cmd response, cmd: " +  std::string(cmd_resp->cmd) + " " + "account_id: " + std::string(cmd_resp->account_id) + " " + "user_id: " + std::string(cmd_resp->user_id) + " " + "data: " + std::string(cmd_resp->data);
    if (m_messageHandler) m_messageHandler("received",msg);
    std::cout << "Received ACK for seq_no: " << cmd_resp->seq_no << std::endl;
    // cout << msg << endl;
}

// 实现所有回调函数
void Client::OnSystemError(const char *error_msg, int sys_errno) {
    // 实现内容
    cout << "System Error: " << error_msg << " syserrno: " << strerror(sys_errno) << endl;
    if (m_statusHandler) {
        m_statusHandler(false, "系统错误:" + std::string(error_msg));
    } else {
        cout << "m_statusHandler is nullptr!" << endl;
    }
}

void Client::OnLoginReject(const typename tcpshm::TcpShmClient<Client>::LoginRspMsg *login_rsp) {
    // 实现内容
    cout << "Login Rejected: " << login_rsp->error_msg << endl;
    if (m_statusHandler) {
        m_statusHandler(false, "登录被拒" + std::string(login_rsp->error_msg));
    } else {
        cout << "m_statusHandler is nullptr!" << endl;
    }
}

int64_t Client::OnLoginSuccess(const typename tcpshm::TcpShmClient<Client>::LoginRspMsg *login_rsp) {
    cout << "Login Success" << endl;

    if (m_statusHandler) {
        m_statusHandler(true, "登录成功");
    } else {
        cout << "m_statusHandler is nullptr!" << endl;
    }

    auto now = std::chrono::system_clock::now();
    int64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
                               now.time_since_epoch()).count();
    return current_time;
}

void Client::OnDisconnected(const char *reason, int sys_errno) {
    cout << "Client disconnected reason: " << reason << " syserrno: " << strerror(sys_errno) << endl;
    if (m_statusHandler) {
        m_statusHandler(false, "连接断开:" + std::string(reason));
    } else {
        cout << "m_statusHandler is nullptr!" << endl;
    }
}

void Client::OnSeqNumberMismatch(uint32_t local_ack_seq,
                                 uint32_t local_seq_start,
                                 uint32_t local_seq_end,
                                 uint32_t remote_ack_seq,
                                 uint32_t remote_seq_start,
                                 uint32_t remote_seq_end) {
    cout << "Seq number mismatch, name: " << conn.GetRemoteName() << " ptcp file: " << conn.GetPtcpFile()
    << " local_ack_seq: " << local_ack_seq << " local_seq_start: " << local_seq_start
    << " local_seq_end: " << local_seq_end << " remote_ack_seq: " << remote_ack_seq
    << " remote_seq_start: " << remote_seq_start << " remote_seq_end: " << remote_seq_end << endl;
    if (m_statusHandler) {
        m_statusHandler(false, "序列号不匹配");
    } else {
        cout << "m_statusHandler is nullptr!" << endl;
    }
}

void Client::OnServerMsg(MsgHeader *header) {
    // 打印消息头详细信息
    cout << "======================== resiveAllMsg =======================" << endl;
    cout << "msg_type: " << header->msg_type << endl;
    cout << "ack_seq: " << header->ack_seq << endl;
    cout << "========================= resiveAllMsg-end =======================" << endl;

    switch (header->msg_type) {
    case MSG_PING_TYPE: {
        handlePing(header->GetMsgBodyPtr<PingResp>());
        break;
    }
    case MSG_CMD_TYPE: {
        handleCmd(header->GetMsgBodyPtr<cmdResp>());
        break;
    }
    case MSG_USER_LOGIN_TYPE:
        cout << "User login success" << endl;
        // 处理用户登录消息
        break;

    case MSG_USER_LOGOUT_TYPE:
        cout << "User logout success" << endl;
        // 处理用户登出消息
        break;

    case MSG_SETTLEMENT_INFO_CONFORM_TYPE:
        cout << "Settlement info conform success" << endl;
        // 处理结算信息确认消息
        break;

    case MSG_QRY_TRADINGACCOUNT_TYPE:
        cout << "Query trading account success" << endl;
        // 处理查询交易账户消息
        break;

    case MSG_ORDER_INSERT_TYPE:
        cout << "Order insert success" << endl;
        // 处理订单插入消息
        break;

    case MSG_ORDER_ACTION_TYPE:
        cout << "Order action success" << endl;
        // 处理订单操作消息
        break;

    case MSG_QRY_INVESTORPOSITION_TYPE:
        cout << "Query investor position success" << endl;
        // 处理查询投资者仓位消息
        break;

    case MSG_QRY_SETTLEMENT_INFO_TYPE:
        cout << "Query settlement info success" << endl;
        // 处理查询结算信息消息
        break;

    case MSG_QRY_TRADE_TYPE:
        cout << "Query trade success" << endl;
        // 处理查询成交消息
        break;

    case MSG_QRY_ORDER_TYPE:
        cout << "Query order success" << endl;
        // 处理查询订单消息
        break;

    case MSG_RECEIVED_TYPE: {
        break;
    }
    default: {
        cout << "Unknown msg type: " << header->msg_type << endl;
        break;
    }
    }
    conn.Pop();
}
