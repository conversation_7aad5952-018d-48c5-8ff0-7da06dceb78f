# Qt5 Docker开发环境配置复盘

## 📋 项目概述

本文档记录了TCP/Qt5客户端压力测试工具的Docker开发环境完整配置过程，包括环境搭建、X11转发配置、容器运行和应用部署的全流程。

## 🏗️ 项目架构

### 技术栈
- **基础系统**: Ubuntu 18.04
- **GUI框架**: Qt5 (支持GUI和headless模式)
- **网络通信**: 自研TCP/SHM高性能通信框架
- **构建系统**: CMake
- **容器化**: Docker + X11转发
- **依赖库**: Boost、RapidJSON、atomic_queue

### 项目结构
```
tcp_qt5/
├── Dockerfile              # Docker镜像定义
├── build-qt5-image.sh      # Docker环境管理脚本
├── CMakeLists.txt          # 主构建配置
├── config.yaml             # 应用配置文件
├── config_template.yaml    # 配置模板
├── src/                    # 源代码目录
│   ├── main.cpp           # 主程序入口
│   ├── mainwindow.*       # Qt主窗口
│   └── tcpshmclientwrapper.* # TCP客户端包装器
├── network/               # 网络通信模块
│   ├── CMakeLists.txt     # 网络模块构建配置
│   ├── PsiTcpShmClient.*  # 核心TCP/SHM客户端
│   └── ...               # 其他网络组件
└── build/                # 构建输出目录
```

## 🐳 Docker环境配置

### 1. Dockerfile配置

**基础镜像选择**: Ubuntu 18.04
```dockerfile
FROM ubuntu:18.04
```

**环境变量设置**:
```dockerfile
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=zh_CN.UTF-8
```

**软件包安装**:
- **基础工具**: build-essential, cmake, git, wget, curl
- **Qt5开发环境**: qt5-default, qtbase5-dev, qttools5-dev
- **网络和系统库**: libboost-all-dev, pkg-config
- **X11支持**: xvfb, x11-apps, libx11-dev
- **调试工具**: gdb, valgrind
- **字体支持**: fonts-dejavu-core, fontconfig

**镜像源优化**:
```dockerfile
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list
```

### 2. 构建脚本 (build-qt5-image.sh)

**主要功能**:
- 镜像构建和管理
- 容器运行和X11转发配置
- 环境验证和清理

**关键特性**:
- 彩色输出和进度提示
- Docker环境检查
- Qt5环境验证
- 自动化容器管理

**使用方法**:
```bash
./build-qt5-image.sh build    # 构建镜像
./build-qt5-image.sh run      # 运行容器
./build-qt5-image.sh clean    # 清理资源
./build-qt5-image.sh info     # 显示信息
```

## 🖥️ X11转发配置

### macOS环境配置

**1. 安装XQuartz**:
```bash
brew install --cask xquartz
```

**2. XQuartz配置**:
- 启动XQuartz应用程序
- 进入偏好设置 → 安全性
- ✅ 勾选"允许从网络客户端连接"
- ✅ 勾选"允许网络连接"
- 重启XQuartz使配置生效

**3. 获取本机IP地址**:
```bash
# 方法1: 使用ifconfig
ifconfig | grep "inet " | grep -v 127.0.0.1

# 方法2: 使用系统偏好设置查看网络信息
```

**4. 设置X11权限**:
```bash
# 允许所有连接(开发环境)
xhost +

# 或者只允许特定IP
xhost +*************
```

### Docker容器X11配置

**容器运行参数**:
```bash
docker run -it \
    --name qt5-dev-container \
    -v "$(pwd):/workspace" \
    -v /tmp/.X11-unix:/tmp/.X11-unix \
    -e DISPLAY=*************:0 \
    --workdir /workspace \
    ubuntu18-qt5-dev:latest
```

**关键配置说明**:
- `-v /tmp/.X11-unix:/tmp/.X11-unix`: 挂载X11套接字
- `-e DISPLAY=*************:0`: 设置显示服务器地址
- `*************`: 替换为实际的macOS主机IP地址

## 🔧 项目构建流程

### 1. 容器内构建步骤

**进入构建目录**:
```bash
cd /workspace
mkdir -p build
cd build
```

**CMake配置**:
```bash
cmake ..
```

**编译项目**:
```bash
make -j$(nproc)
```

**复制配置文件**:
```bash
cp /workspace/config.yaml /workspace/build/
```

### 2. 应用程序运行

**基本运行**:
```bash
./tcp_qt5
```

**命令行参数支持**:
```bash
# 指定配置文件
./tcp_qt5 --config config.yaml

# 设置客户端名称
./tcp_qt5 --client-name test_client_01

# 指定服务器地址和端口
./tcp_qt5 --server 127.0.0.1 --port 8888

# 无界面模式(压力测试)
./tcp_qt5 --headless --test-duration 60

# 设置发送间隔
./tcp_qt5 --send-interval 1000
```

## ⚠️ 常见问题与解决方案

### 1. X11连接问题

**问题现象**:
```
QStandardPaths: XDG_RUNTIME_DIR not set
Authorization required, but no authorization protocol specified
Could not connect to any X display
```

**解决方案**:
1. **检查XQuartz配置**:
   - 确保"允许从网络客户端连接"已勾选
   - 重启XQuartz应用程序

2. **验证IP地址**:
   ```bash
   # 在macOS上获取正确的IP地址
   ifconfig en0 | grep "inet " | awk '{print $2}'
   ```

3. **设置X11权限**:
   ```bash
   xhost +$(ifconfig en0 | grep "inet " | awk '{print $2}')
   ```

4. **更新Docker运行命令**:
   ```bash
   # 使用正确的IP地址
   docker run -it \
       -e DISPLAY=$(ifconfig en0 | grep "inet " | awk '{print $2}'):0 \
       # ... 其他参数
   ```

### 2. 配置文件问题

**问题现象**:
```
配置文件不存在: "config.yaml"
```

**解决方案**:
```bash
# 确保配置文件存在于构建目录
cp /workspace/config.yaml /workspace/build/

# 或者使用绝对路径
./tcp_qt5 --config /workspace/config.yaml
```

### 3. 构建依赖问题

**CMake配置失败**:
- 检查Qt5是否正确安装
- 验证Boost库是否可用
- 确认所有依赖包已安装

**编译错误**:
- 检查C++标准支持(项目使用C++17)
- 验证头文件路径配置
- 确认链接库设置正确

## 🚀 最佳实践

### 1. 开发环境管理

**使用脚本自动化**:
```bash
# 一键构建和运行
./build-qt5-image.sh run

# 开发完成后清理
./build-qt5-image.sh clean
```

**容器数据持久化**:
- 使用卷挂载保持源代码同步
- 构建产物保存在宿主机
- 配置文件版本控制

### 2. 调试和测试

**GUI应用调试**:
```bash
# 启用调试模式
gdb ./tcp_qt5

# 内存检查
valgrind --tool=memcheck ./tcp_qt5
```

**网络连接测试**:
```bash
# 测试X11连接
xclock  # 应该显示时钟窗口

# 测试Qt应用
./tcp_qt5 --headless  # 无界面模式测试
```

### 3. 性能优化

**构建优化**:
- 使用多核编译: `make -j$(nproc)`
- 启用编译缓存
- 优化Docker镜像层

**运行时优化**:
- 合理设置发送间隔
- 监控系统资源使用
- 调整TCP缓冲区大小

## 📊 环境验证清单

### ✅ 基础环境检查
- [ ] Docker已安装并运行
- [ ] XQuartz已安装并配置
- [ ] 网络连接正常
- [ ] IP地址获取正确

### ✅ 容器环境检查
- [ ] 镜像构建成功
- [ ] 容器启动正常
- [ ] 工作目录挂载正确
- [ ] X11转发配置生效

### ✅ 应用程序检查
- [ ] CMake配置成功
- [ ] 编译无错误
- [ ] 配置文件加载正常
- [ ] GUI界面显示正常
- [ ] 网络连接功能正常

## 🔄 持续改进建议

### 1. 自动化脚本增强
- 添加IP地址自动检测
- 实现配置文件自动生成
- 集成测试用例执行

### 2. 环境标准化
- 创建开发环境Docker Compose配置
- 建立CI/CD流水线
- 添加环境健康检查

### 3. 文档完善
- 添加故障排除指南
- 创建快速开始教程
- 维护常见问题FAQ

## 📝 总结

通过Docker容器化和X11转发技术，成功搭建了跨平台的Qt5开发环境。关键成功因素包括：

1. **正确的X11转发配置**: XQuartz设置和网络权限
2. **完整的依赖管理**: Dockerfile中的软件包安装
3. **自动化脚本支持**: 简化环境管理和部署流程
4. **灵活的配置系统**: 支持多种运行模式和参数

该环境配置为TCP客户端压力测试工具提供了稳定、可重复的开发和测试平台，支持GUI和headless两种运行模式，满足不同场景的使用需求。

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: 开发团队