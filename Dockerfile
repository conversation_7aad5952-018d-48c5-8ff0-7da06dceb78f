# Ubuntu 18.04 + Qt5 开发环境
FROM ubuntu:18.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /workspace

# 配置国内镜像源加速下载
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/ports.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 更新包管理器并安装基础工具和Qt5开发环境
# 在现有的apt-get install命令中添加
RUN apt-get update && apt-get install -y \
    # 基础系统工具
    tzdata \
    locales \
    ca-certificates \
    # 开发工具链
    build-essential \
    cmake \
    make \
    git \
    pkg-config \
    # 网络和下载工具
    wget \
    curl \
    # Qt5核心开发包
    qtbase5-dev \
    qtbase5-dev-tools \
    qt5-qmake \
    qttools5-dev \
    qttools5-dev-tools \
    # Qt5常用模块
    qtdeclarative5-dev \
    qtmultimedia5-dev \
    libqt5svg5-dev \
    libqt5x11extras5-dev \
    # OpenGL和图形库
    libgl1-mesa-dev \
    libglu1-mesa-dev \
    libxrender-dev \
    libxrandr-dev \
    libxss-dev \
    libgconf-2-4 \
    libxcomposite-dev \
    libxdamage-dev \
    # Boost开发库
    libboost-all-dev \
    # RapidJSON开发库
    rapidjson-dev \
    # Redis客户端库
    libhiredis-dev \
    # YAML解析库
    libyaml-cpp-dev \
    # fmt格式化库
    libfmt-dev \
    # 字体和输入法支持
    fonts-dejavu-core \
    fontconfig \
    # 调试工具
    gdb \
    valgrind \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置中文locale
RUN locale-gen zh_CN.UTF-8
ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh
ENV LC_ALL=zh_CN.UTF-8

# 设置Qt5环境变量
ENV QT_SELECT=5
ENV QTDIR=/usr/lib/qt5
ENV PATH="$QTDIR/bin:$PATH"
ENV LD_LIBRARY_PATH="$QTDIR/lib:$LD_LIBRARY_PATH"
ENV PKG_CONFIG_PATH="$QTDIR/lib/pkgconfig:$PKG_CONFIG_PATH"

# 创建非root用户 (已注释，避免sudo权限问题)
# RUN useradd -m -s /bin/bash developer && \
#     echo 'developer:developer' | chpasswd && \
#     usermod -aG sudo developer

# 验证Qt5安装
RUN qmake --version && \
    echo "Qt5 development environment ready!" && \
    echo "CMake version: $(cmake --version | head -n1)" && \
    echo "GCC version: $(gcc --version | head -n1)"

# 设置默认用户 (已注释，因为没有创建非root用户)
# USER developer
# WORKDIR /home/<USER>

# 使用root用户，工作目录设为/workspace
WORKDIR /workspace

# 默认启动bash
CMD ["/bin/bash"]