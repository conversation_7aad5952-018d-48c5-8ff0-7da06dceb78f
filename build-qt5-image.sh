#!/bin/bash

# Ubuntu 18.04 + Qt5 Docker镜像构建脚本
# 作者: Assistant
# 用途: 一键构建和管理Qt5开发环境Docker镜像

set -e  # 遇到错误立即退出

# 配置变量
IMAGE_NAME="ubuntu18-qt5-dev"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
DOCKERFILE_PATH="./Dockerfile"
CONTAINER_NAME="qt5-dev-container"

# 颜色输出函数
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
}

# 检查Dockerfile是否存在
check_dockerfile() {
    if [ ! -f "$DOCKERFILE_PATH" ]; then
        print_error "Dockerfile不存在: $DOCKERFILE_PATH"
        exit 1
    fi
}

# 构建Docker镜像
build_image() {
    print_info "开始构建Docker镜像: $FULL_IMAGE_NAME"
    print_info "使用Dockerfile: $DOCKERFILE_PATH"
    
    # 显示构建进度
    if docker build -t "$FULL_IMAGE_NAME" -f "$DOCKERFILE_PATH" .; then
        print_success "Docker镜像构建成功: $FULL_IMAGE_NAME"
        
        # 显示镜像信息
        print_info "镜像详细信息:"
        docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        
        # 验证Qt5安装
        print_info "验证Qt5环境..."
        if docker run --rm "$FULL_IMAGE_NAME" qmake --version; then
            print_success "Qt5环境验证成功"
        else
            print_warning "Qt5环境验证失败，但镜像已构建完成"
        fi
    else
        print_error "Docker镜像构建失败"
        exit 1
    fi
}

# 运行容器
# 在run_container函数中，将DISPLAY配置修改为动态获取IP
run_container() {
    print_info "启动Qt5开发容器..."
    
    # 停止并删除已存在的容器
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_info "停止并删除已存在的容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" &> /dev/null || true
        docker rm "$CONTAINER_NAME" &> /dev/null || true
    fi
    
    # 获取本机IP地址
    LOCAL_IP=$(ipconfig getifaddr en0 2>/dev/null || echo "host.docker.internal")
    print_info "使用显示服务器: ${LOCAL_IP}:0"
    
    # 启动新容器，添加X11转发支持
    docker run -it \
        --name "$CONTAINER_NAME" \
        --hostname qt5-dev \
        -v "$(pwd):/workspace" \
        -v "$HOME/.gitconfig:/home/<USER>/.gitconfig:ro" \
        -v /tmp/.X11-unix:/tmp/.X11-unix \
        -e DISPLAY="${LOCAL_IP}:0" \
        -e XDG_RUNTIME_DIR="/tmp/runtime-developer" \
        --workdir /workspace \
        "$FULL_IMAGE_NAME"
}

# 清理镜像和容器
clean() {
    print_info "清理Docker资源..."
    
    # 停止并删除容器
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_info "删除容器: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" &> /dev/null || true
        docker rm "$CONTAINER_NAME" &> /dev/null || true
    fi
    
    # 删除镜像
    if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${FULL_IMAGE_NAME}$"; then
        print_info "删除镜像: $FULL_IMAGE_NAME"
        docker rmi "$FULL_IMAGE_NAME"
    fi
    
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Ubuntu 18.04 + Qt5 Docker环境管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build    构建Docker镜像"
    echo "  run      运行开发容器"
    echo "  clean    清理镜像和容器"
    echo "  info     显示镜像信息"
    echo "  help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build    # 构建镜像"
    echo "  $0 run      # 运行容器"
    echo "  $0 clean    # 清理资源"
    echo ""
    echo "镜像信息:"
    echo "  名称: $FULL_IMAGE_NAME"
    echo "  容器名: $CONTAINER_NAME"
}

# 显示镜像信息
show_info() {
    print_info "Docker镜像信息:"
    if docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep -v "REPOSITORY"; then
        echo ""
        print_info "容器信息:"
        if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"; then
            echo ""
        else
            print_info "没有找到相关容器"
        fi
    else
        print_warning "镜像 $FULL_IMAGE_NAME 不存在，请先运行构建命令"
    fi
}

# 主函数
main() {
    # 检查Docker环境
    check_docker
    
    case "${1:-build}" in
        "build")
            check_dockerfile
            build_image
            ;;
        "run")
            if ! docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${FULL_IMAGE_NAME}$"; then
                print_warning "镜像不存在，先构建镜像..."
                check_dockerfile
                build_image
            fi
            run_container
            ;;
        "clean")
            clean
            ;;
        "info")
            show_info
            ;;
        "help"|"--help"|"h")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"