#!/bin/bash
# 超简单的测试脚本

echo "TCP客户端简单测试"
echo "=================="




# 启动3个客户端（后台运行）
./build/tcp_qt5 --headless --client-name test2_1 --test-duration 10 --send-interval 1001 --message-size 128 --server 127.0.0.1 --port 8888 &
./build/tcp_qt5 --headless --client-name test2_2 --test-duration 11 --send-interval 100220 --message-size 128 --server 127.0.0.1 --port 8888 &
./build/tcp_qt5 --headless --client-name test2_3 --test-duration 12 --send-interval 1003330 --message-size 128 --server 127.0.0.1 --port 8888 &


echo "测试完成！日志文件在 test_results/ 目录"
