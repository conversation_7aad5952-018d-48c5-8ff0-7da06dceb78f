#!/bin/bash
# 最简单的测试脚本 - 直接运行

echo "快速测试开始..."

# 创建结果目录
mkdir -p test_results

echo "测试1: 1个客户端，10秒"
./build/tcp_qt5 --headless --client-name test1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888

echo ""
echo "等待3秒..."
sleep 3

echo "测试2: 3个客户端，10秒"
./build/tcp_qt5 --headless --client-name test2_1 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_1.log 2>&1 &
./build/tcp_qt5 --headless --client-name test2_2 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_2.log 2>&1 &
./build/tcp_qt5 --headless --client-name test2_3 --test-duration 10 --send-interval 1000 --message-size 128 --server 127.0.0.1 --port 8888 > test_results/test2_3.log 2>&1 &

echo "等待10秒..."
sleep 11

echo ""
echo "测试完成！"
echo "日志文件在 test_results/ 目录"
