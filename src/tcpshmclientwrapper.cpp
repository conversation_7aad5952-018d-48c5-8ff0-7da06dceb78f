#include "tcpshmclientwrapper.h"
#include <QHBoxLayout>
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
#include <QStringDecoder>
#endif

TcpShmClientWrapper::TcpShmClientWrapper(QString clientName,QWidget *parent)
    : QWidget{parent}
{
    qInfo()<<"[TcpShmClientWrapper] clientName="<<clientName;
    QString configPath = QCoreApplication::applicationDirPath() + "/config.yaml";

    TcpShmConfig config = parseConfig(configPath);

    // 初始化配置
    m_clientConf.Index = 1;
    m_clientConf.CpuCore = 0; // 不绑定核心

    // 优先使用命令行传入的客户端名称，如果为空则使用配置文件中的
    QString finalClientName = clientName.isEmpty() ? config.clientName : clientName;
    qInfo() << "[TcpShmClientWrapper] Using client name:" << finalClientName
            << "(from" << (clientName.isEmpty() ? "config" : "command line") << ")";

    m_clientConf.AccountId = finalClientName.toStdString();
    m_clientConf.ServerAddr = config.serverAddr.toStdString();
    m_clientConf.ServerPort = config.serverPort;
    m_clientConf.ConnectionTimeout = config.connectionTimeout;
    m_clientConf.HeartBeatInverval = config.heartBeatInterval;

    // 打印配置信息
    qInfo() << "=========================";
    qInfo() << "--- 客户端配置信息 ---";
    qInfo() << "客户端名称:" << finalClientName;
    qInfo() << "服务器地址:" << QString::fromStdString(m_clientConf.ServerAddr);
    qInfo() << "服务器端口:" << m_clientConf.ServerPort;
    qInfo() << "连接超时:" << m_clientConf.ConnectionTimeout << "ms";
    qInfo() << "心跳间隔:" << m_clientConf.HeartBeatInverval << "ms";
    qInfo() << "使用共享内存:" << (config.useShm ? "true" : "false");
    qInfo() << "=========================";
    // 选择 PTCP 目录：优先使用可执行文件目录下的 ptcp 目录
    QString qtPath = QCoreApplication::applicationDirPath() + "/ptcp";
    QByteArray utf8Data = qtPath.toUtf8();
    const std::string stdPath(utf8Data.constData(), utf8Data.length());
    // 创建客户端实例 - 使用最终确定的客户端名称
    m_client = new Client(stdPath, finalClientName.toStdString(), m_clientConf);
    m_client->m_useShm = config.useShm; // 由配置控制是否使用共享内存

    // 初始化文本显示区域
    textEdit = new QTextEdit(this);
    textEdit->setReadOnly(true); // 设置为只读
    textEdit->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn); // 始终显示垂直滚动条

    QWidget * statusContainer = new QWidget(this);
    QHBoxLayout * statusLayout = new QHBoxLayout(statusContainer);
    statusLayout->setContentsMargins(0,0,0,0);
    statusLayout->setSpacing(5);

    statusIndicator = new QLabel();
    statusIndicator->setFixedSize(12,12);
    statusIndicator->setStyleSheet("border-radius:6px;background:red;");

    statusLabel = new QLabel("连接中...");
    statusLayout->addWidget(statusIndicator);
    statusLayout->addWidget(statusLabel);

    QVBoxLayout * mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(10,10,10,10);
    mainLayout->addWidget(textEdit);
    mainLayout->addWidget(statusContainer);

    updateConnectStatus(false,"连接中...");

    // Qt5.9 兼容：信号-槽（队列连接）确保跨线程回到UI
    connect(this, &TcpShmClientWrapper::sigUpdateStatus,
            this, &TcpShmClientWrapper::onUpdateStatus,
            Qt::QueuedConnection);
    connect(this, &TcpShmClientWrapper::sigAppendMessage,
            this, &TcpShmClientWrapper::onAppendMessage,
            Qt::QueuedConnection);

    // 设置状态回调
    m_client->setStatusHandler([this](bool connected, const std::string &text) {
        updateConnectStatus(connected, QString::fromStdString(text));
    });

    // 不在构造函数中启动 Run，等运行时参数设置后再启动

    qInfo() << "[TcpShmClientWrapper] constructed, waiting runtime params to start Run()";

    // 设置回调函数，Client 调用它来传递消息
    setMessageCallback([this](const QString &flag,const QString &msg) {
        appendMessage(flag,msg);
    });
}

// 函数: updateConnectStatus
// 作用: 线程安全地请求UI更新（发信号，槽内执行UI）
void TcpShmClientWrapper::updateConnectStatus(bool connected, QString text)
{
    emit sigUpdateStatus(connected, text);
}

// 函数: onUpdateStatus
// 作用: 在UI线程更新连接状态与文字
void TcpShmClientWrapper::onUpdateStatus(bool connected, const QString &text)
{
    if (connected)
        statusIndicator->setStyleSheet("border-radius:6px;background:green;");
    else
        statusIndicator->setStyleSheet("border-radius:6px;background:red;");
    statusLabel->setText(text);
}

TcpShmConfig TcpShmClientWrapper::parseConfig(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "Cannot open config file";
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        // 直接输出 QString，避免 QDebug 与 std::string 的重载歧义
        qDebug() << error.errorString();
    }

    TcpShmConfig config;
    QJsonObject root = doc.object();
    QJsonObject traderClient = root["tcpshm"].toObject()
                                   ["client"].toObject()
                                           ["trader_client"].toObject();

    config.choice = traderClient["choice"].toInt();

    QJsonArray options = traderClient["options"].toArray();
    if (!options.isEmpty()) {
        QJsonObject option = options[0].toObject();
        config.clientName = option["client_name"].toString();
        config.serverAddr = option["server_addr"].toString();
        config.serverPort = option["server_port"].toInt();
        config.useShm = option["use_shm"].toBool();
        config.tcpRecvBufInitSize = option["tcp_recv_buf_init_size"].toInt();
        config.tcpRecvBufMaxSize = option["tcp_recv_buf_max_size"].toInt();
        config.tcpNoDelay = option["tcp_no_delay"].toBool();
        config.connectionTimeout = option["connection_timeout"].toVariant().toLongLong();
        config.heartBeatInterval = option["heart_beat_interval"].toVariant().toLongLong();
    }

    return config;
}

void TcpShmClientWrapper::setMessageCallback(MessageCallback callback) {
    m_callback = callback;
    if (m_client) {
        m_client->setMessageHandler([callback](const std::string &flag,const std::string &msg) {
            callback(QString::fromStdString(flag),QString::fromStdString(msg));
        });
    }
}

// GBK 转 UTF-8 - 兼容Qt5和Qt6
QString gbkToUtf8(const QByteArray &gbkData)
{
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
    QStringDecoder decoder(QStringDecoder::System); // Qt6方式
    return decoder.decode(gbkData);
#else
    QTextCodec *codec = QTextCodec::codecForName("GBK"); // Qt5方式
    if (codec) {
        return codec->toUnicode(gbkData);
    }
    return QString::fromLocal8Bit(gbkData); // 备用方案
#endif
}

void TcpShmClientWrapper::appendMessage(const QString &flag,const QString &message)
{
    // 将可能GBK编码的消息转换为UTF-8
    QString utf8Message = QString::fromLocal8Bit(message.toLocal8Bit());

    // 通过信号-槽（Queued）在UI线程执行
    emit sigAppendMessage(flag, utf8Message);
}

// 函数: onAppendMessage
// 作用: 在UI线程向文本框追加消息并滚动到底部
void TcpShmClientWrapper::onAppendMessage(const QString &flag, const QString &utf8Message)
{
    QTextCursor cursor = textEdit->textCursor();
    cursor.movePosition(QTextCursor::End);

    // 设置文本块格式
    QTextBlockFormat blockFormat;
    blockFormat.setTopMargin(5);
    blockFormat.setBottomMargin(5);
    cursor.setBlockFormat(blockFormat);

    // 设置字符格式
    QTextCharFormat charFormat;
    if (flag == "sent") {
        charFormat.setBackground(QColor(232, 248, 255));
    } else {
        charFormat.setBackground(QColor(220, 220, 220));
    }
    cursor.setCharFormat(charFormat);

    // 插入文本
    cursor.insertText(" " + utf8Message + " ");
    cursor.insertText("\n");

    // 自动滚动到底部
    QScrollBar *scrollBar = textEdit->verticalScrollBar();
    scrollBar->setValue(scrollBar->maximum());
}

void TcpShmClientWrapper::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);  // 调用父类的 resizeEvent
}

/**
 * 设置运行时参数并传递给客户端
 * @param testDuration 测试持续时间（秒）
 * @param sendInterval 发送间隔（微秒）
 * @param messageSize 消息大小（字节）
 * @param headlessMode 无界面模式
 * @param configFile 配置文件路径
 */
void TcpShmClientWrapper::setRuntimeParams(int testDuration, int sendInterval, int messageSize, bool headlessMode, const QString& configFile)
{
    qInfo() << "[TcpShmClientWrapper::setRuntimeParams]" << "dur=" << testDuration
            << "send=" << sendInterval << "size=" << messageSize << "headless=" << headlessMode
            << "config=" << configFile;

    // 打印运行时参数（如果已设置）
    if (testDuration > 0 || sendInterval > 0 || messageSize > 0) {
        qInfo() << "TcpShmClientWrapper=========================";
        qInfo() << "--- 运行时参数 ---";
        qInfo() << "测试持续时间 (TestDuration):" << (testDuration > 0 ? QString("%1 秒").arg(testDuration) : "无限制");
        qInfo() << "发送间隔 (SendInterval):" << sendInterval << "微秒";
        qInfo() << "消息大小 (MessageSize):" << messageSize << "字节";
        qInfo() << "无界面模式 (HeadlessMode):" << (headlessMode ? "true" : "false");
        qInfo() << "=========================";
    }

    if (m_client) {
        m_client->setRuntimeParams(testDuration, sendInterval, messageSize, headlessMode, configFile.toStdString());
        if (!m_started) { qInfo() << "[TcpShmClientWrapper] starting Client::Run()"; m_client->Run(); m_started = true; }
    }
}

/**
 * 设置服务器连接参数（用于命令行参数覆盖配置文件）
 * @param serverAddr 服务器地址
 * @param serverPort 服务器端口
 */
void TcpShmClientWrapper::setServerParams(const QString& serverAddr, int serverPort)
{
    qInfo() << "[TcpShmClientWrapper::setServerParams] addr=" << serverAddr << "port=" << serverPort;

    // 更新配置
    m_clientConf.ServerAddr = serverAddr.toStdString();
    m_clientConf.ServerPort = serverPort;

    qInfo() << "--- 服务器参数更新 ---";
    qInfo() << "新服务器地址:" << serverAddr;
    qInfo() << "新服务器端口:" << serverPort;
    qInfo() << "参数来源: 命令行覆盖";

    // 如果客户端已创建，需要更新其配置
    if (m_client) {
        m_client->m_serverAddress = serverAddr.toStdString();
        m_client->m_serverPort = serverPort;
        qInfo() << "[TcpShmClientWrapper] Updated server params in existing client";
    }
}

TcpShmClientWrapper::~TcpShmClientWrapper()
{
    delete m_client;
}
