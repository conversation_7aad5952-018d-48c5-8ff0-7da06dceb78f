#include "mainwindow.h"
#include <QApplication>
#include <QWidget>
#include <QResizeEvent>
#include <QPixmap>
// #include "ui_mainwindow.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_serverPort(8888), m_sendInterval(1000)
{
    // ui->setupUi(this);
    this->resize(1200,800);
    this->setWindowTitle(Global::APP_TITLE);
    this->setWindowIcon(QPixmap(":/Images/logo.png"));
    qDebug()<<"mainwindow client_name_";
    // 延迟创建客户端，等待client_name_被设置
    client = nullptr;
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);  // 调用父类的 resizeEvent
    client->setFixedSize(this->width(),this->height());
}

MainWindow::~MainWindow()
{
    // delete ui;
    delete client;
}

/**
 * 设置配置文件路径
 * @param configFile 配置文件路径
 */
void MainWindow::setConfigFile(const QString& configFile)
{
    m_configFile = configFile;
    if (client) {
        // 如果需要，可以通知客户端重新加载配置
        // client->loadConfig(configFile);
    }
}

/**
 * 设置客户端名称
 * @param clientName 客户端名称
 */
void MainWindow::setClientName(const QString& clientName)
{
    m_clientName = clientName;
    client_name_ = clientName;

    // 创建客户端（如果还没有创建）
    if (client == nullptr) {
        client = new TcpShmClientWrapper(client_name_, this);
        qInfo() << "[MainWindow::setClientName] wrapper created for" << client_name_;
    }

    // 更新窗口标题以包含客户端名称
    QString title = QString("%1 - %2").arg(Global::APP_TITLE).arg(clientName);
    this->setWindowTitle(title);
}

/**
 * 设置服务器地址和端口
 * @param address 服务器地址
 * @param port 服务器端口
 */
void MainWindow::setServerAddress(const QString& address, int port)
{
    m_serverAddress = address;
    m_serverPort = port;
    if (client) {
        // 如果需要，可以通知客户端更新服务器地址
        // client->setServerAddress(address, port);
    }
}

/**
 * 设置发送间隔
 * @param intervalUs 发送间隔(微秒)
 */
void MainWindow::setSendInterval(int intervalUs)
{
    m_sendInterval = intervalUs;
    if (client) {
        // 如果需要，可以通知客户端更新发送间隔
        // client->setSendInterval(intervalUs);
    }
}

/**
 * 设置消息大小
 * @param messageSize 消息大小(字节)
 */
void MainWindow::setMessageSize(int messageSize)
{
    m_messageSize = messageSize;
    if (client) {
        // 如果需要，可以通知客户端更新消息大小
        // client->setMessageSize(messageSize);
    }
}

/**
 * 设置所有运行时参数并传递给客户端
 * @param testDuration 测试持续时间（秒）
 * @param sendInterval 发送间隔（微秒）
 * @param messageSize 消息大小（字节）
 * @param headlessMode 无界面模式
 * @param configFile 配置文件路径
 */
void MainWindow::setAllRuntimeParams(int testDuration, int sendInterval, int messageSize, bool headlessMode, const QString& configFile)
{
    // 保存参数
    m_sendInterval = sendInterval;
    m_messageSize = messageSize;
    m_configFile = configFile;

    // 如果客户端已创建，传递参数
    if (client) {
        client->setRuntimeParams(testDuration, sendInterval, messageSize, headlessMode, configFile);
    }
}
