#ifndef TCPSHMCLIENTWRAPPER_H
#define TCPSHMCLIENTWRAPPER_H

#include <QWidget>
#include <QThread>
#include <QDebug>
#include <QCoreApplication>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "../network/PsiTcpShmClient.h"
#include <QScrollBar>
#include <QTextEdit>
#include <QLabel>
#include <QTextFrame>
#include <QTextCharFormat>
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
#include <QTextCodec>
#endif

struct TcpShmConfig {
    int choice;
    QString clientName;
    QString serverAddr;
    int serverPort;
    bool useShm;
    int tcpRecvBufInitSize;
    int tcpRecvBufMaxSize;
    bool tcpNoDelay;
    qint64 connectionTimeout;
    qint64 heartBeatInterval;
};

class TcpShmClientWrapper : public QWidget
{
    Q_OBJECT

public:
    explicit TcpShmClientWrapper(QString clientName,QWidget *parent = nullptr);
    ~TcpShmClientWrapper();
    void resizeEvent(QResizeEvent *event) override;  // 重写 resizeEvent

    // 设置运行时参数的方法
    void setRuntimeParams(int testDuration, int sendInterval, int messageSize, bool headlessMode, const QString& configFile);

    // 设置服务器连接参数的方法（用于命令行参数覆盖配置文件）
    void setServerParams(const QString& serverAddr, int serverPort);

private:
    bool m_started = false;

    // 回调函数类型
    using MessageCallback = std::function<void(const QString&,const QString&)>;

    // 设置回调函数,Client调用它来传递消息
    void setMessageCallback(MessageCallback callback);

    // 添加消息到显示区域
    void appendMessage(const QString &flag,const QString &message);

    // 更新连接状态
    void updateConnectStatus(bool connected,QString text);


signals:
    // 函数: sigUpdateStatus
    // 作用: 跨线程通知UI更新连接状态（Qt::QueuedConnection）
    void sigUpdateStatus(bool connected, const QString &text);

    // 函数: sigAppendMessage
    // 作用: 跨线程通知UI追加一条消息（Qt::QueuedConnection）
    void sigAppendMessage(const QString &flag, const QString &message);

private slots:
    // 函数: onUpdateStatus
    // 作用: 在UI线程中更新状态指示灯与文本
    void onUpdateStatus(bool connected, const QString &text);

    // 函数: onAppendMessage
    // 作用: 在UI线程中向文本框追加消息并滚动到底部
    void onAppendMessage(const QString &flag, const QString &message);

private:
    Client *m_client = nullptr;
    ClientConf m_clientConf;
    TcpShmConfig parseConfig(const QString& filePath);
    MessageCallback m_callback;
    QTextEdit *textEdit;
    QLabel * statusIndicator;
    QLabel * statusLabel;
};

#endif // TCPSHMCLIENTWRAPPER_H
