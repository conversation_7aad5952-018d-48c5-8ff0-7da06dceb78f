#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QWidget>
#include <QResizeEvent>
#include <QString>
#include <QPixmap>
#include "globals.h"
#include "tcpshmclientwrapper.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    void resizeEvent(QResizeEvent *event) override;  // 重写 resizeEvent
    
    // 新增的参数设置方法
    void setConfigFile(const QString& configFile);
    void setClientName(const QString& clientName);
    void setServerAddress(const QString& address, int port);
    void setSendInterval(int intervalUs);
    void setMessageSize(int messageSize);  // 添加这个方法
    void setAllRuntimeParams(int testDuration, int sendInterval, int messageSize, bool headlessMode, const QString& configFile);
    QString client_name_;
    
private:
    // Ui::MainWindow *ui;
    TcpShmClientWrapper * client = nullptr;
    
    // 存储命令行参数
    QString m_configFile;
    QString m_clientName;
    QString m_serverAddress;
    int m_serverPort;
    int m_sendInterval;
    int m_messageSize;  // 添加消息大小成员变量
};

#endif // MAINWINDOW_H
